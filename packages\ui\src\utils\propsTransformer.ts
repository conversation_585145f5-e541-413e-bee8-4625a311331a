/**
 * Props 转换器工具
 * 用于统一处理组件属性的传递、过滤和转换
 */

import type { ComputedRef } from 'vue'

/** 属性过滤配置 */
export interface PropsFilterConfig {
  /** 要包含的属性列表 */
  include?: string[]
  /** 要排除的属性列表 */
  exclude?: string[]
  /** 属性映射规则 */
  mapping?: Record<string, string | ((value: any, context?: any) => any)>
  /** 默认值 */
  defaults?: Record<string, any>
  /** 条件属性 - 只有满足条件才包含 */
  conditional?: Record<string, (props: any, context?: any) => boolean>
}

/**
 * 创建属性过滤器
 */
export function createPropsFilter(config: PropsFilterConfig = {}) {
  const {
    include,
    exclude,
    mapping = {},
    defaults = {},
    conditional = {},
  } = config

  return function filterProps(
    sourceProps: any,
    context?: any
  ): Record<string, any> {
    const result: Record<string, any> = { ...defaults }

    // 获取要处理的属性键
    let keys = Object.keys(sourceProps)

    // 应用 include 过滤
    if (include) {
      keys = keys.filter(key => include.includes(key))
    }

    // 应用 exclude 过滤
    if (exclude) {
      keys = keys.filter(key => !exclude.includes(key))
    }

    // 处理每个属性
    for (const key of keys) {
      const value = sourceProps[key]

      // 检查条件属性
      if (conditional[key] && !conditional[key](sourceProps, context)) {
        continue
      }

      // 应用映射规则
      if (mapping[key]) {
        const mapper = mapping[key]
        if (typeof mapper === 'string') {
          // 重命名属性
          result[mapper] = value
        } else if (typeof mapper === 'function') {
          // 转换属性值
          result[key] = mapper(value, context)
        }
      } else {
        // 直接复制
        result[key] = value
      }
    }

    return result
  }
}

/**
 * 解包计算属性值
 */
export function unwrapComputedValues(
  obj: Record<string, any>
): Record<string, any> {
  const result: Record<string, any> = {}

  Object.entries(obj).forEach(([key, value]) => {
    if (value && typeof value === 'object' && 'value' in value) {
      // ComputedRef
      result[key] = value.value
    } else {
      // 普通值
      result[key] = value
    }
  })

  return result
}

/**
 * 合并 slot props
 */
export function mergeSlotProps(
  baseProps: Record<string, any>,
  slotProps: any,
  overrides: Record<string, any> = {}
) {
  return {
    ...baseProps,
    // 从 slotProps 中提取特定属性
    id: slotProps?.fieldId,
    placeholder: slotProps?.placeholder,
    disabled: slotProps?.disabled,
    readonly: slotProps?.readonly,
    class: slotProps?.inputClasses,
    style: slotProps?.inputStyle,
    // 应用覆盖属性
    ...overrides,
  }
}

/**
 * InputField 专用的属性处理器
 */
export class InputFieldPropsProcessor {
  private baseInputFilter: ReturnType<typeof createPropsFilter>
  private containerFilter: ReturnType<typeof createPropsFilter>

  // 缓存系统
  private propsCache = new Map<string, { result: any; timestamp: number }>()
  private readonly CACHE_TTL = 50 // 缓存有效期 50ms
  private readonly MAX_CACHE_SIZE = 100 // 最大缓存条目数

  constructor() {
    // BaseInput 属性过滤器
    this.baseInputFilter = createPropsFilter({
      include: [
        'maxlength',
        'minlength',
        'autocomplete',
        'autofocus',
        'step',
        'min',
        'max',
      ],
    })

    // FieldContainer 属性过滤器
    this.containerFilter = createPropsFilter({
      exclude: [
        // 排除 BaseInput 特有的属性
        'maxlength',
        'minlength',
        'autocomplete',
        'autofocus',
        'step',
        'min',
        'max',
        'inputmode',
        // 排除控件系统相关属性
        'clearable',
        'showPassword',
        'showWordLimit',
        'smsCountdown',
        'smsSendText',
        'smsResendText',
      ],
      mapping: {
        // 属性转换规则
        variant: (value: string) => (value === 'pill' ? 'default' : value),
        effect: (value: string) => (value === 'shadow' ? 'glow' : value),
        size: (value: any) => (typeof value === 'number' ? 'medium' : value),
      },
    })
  }

  /**
   * 生成缓存键
   */
  private createCacheKey(prefix: string, props: any, extras?: any): string {
    // 简化的缓存键生成（实际项目中可能需要更复杂的哈希）
    const propsKeys = Object.keys(props).sort().join(',')
    const extrasStr = extras ? JSON.stringify(extras) : ''
    return `${prefix}:${propsKeys}:${extrasStr}`
  }

  /**
   * 获取缓存结果
   */
  private getCachedResult<T>(key: string): T | null {
    const cached = this.propsCache.get(key)
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.result as T
    }
    return null
  }

  /**
   * 设置缓存结果
   */
  private setCachedResult(key: string, result: any): void {
    // 如果缓存过大，清理过期条目
    if (this.propsCache.size >= this.MAX_CACHE_SIZE) {
      this.cleanExpiredCache()
    }

    this.propsCache.set(key, {
      result,
      timestamp: Date.now(),
    })
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now()
    for (const [key, cached] of this.propsCache.entries()) {
      if (now - cached.timestamp >= this.CACHE_TTL) {
        this.propsCache.delete(key)
      }
    }
  }

  /**
   * 生成 BaseInput 属性（带缓存）
   */
  createBaseInputProps(
    props: any,
    slotProps: any,
    computedValues: {
      mappedInputType: ComputedRef<string>
      actualInputMode: ComputedRef<string | undefined>
    }
  ) {
    const cacheKey = this.createCacheKey('baseInput', props, {
      type: computedValues.mappedInputType.value,
      inputmode: computedValues.actualInputMode.value,
      slotPropsKeys: Object.keys(slotProps || {})
        .sort()
        .join(','),
    })

    // 尝试从缓存获取
    const cached = this.getCachedResult(cacheKey)
    if (cached) {
      return cached
    }

    // 计算新结果
    const filteredProps = this.baseInputFilter(props)
    const result = mergeSlotProps(filteredProps, slotProps, {
      type: computedValues.mappedInputType.value,
      inputmode: computedValues.actualInputMode.value,
    })

    // 缓存结果
    this.setCachedResult(cacheKey, result)
    return result
  }

  /**
   * 生成 FieldContainer 属性
   */
  createContainerProps(
    props: any,
    computedValues: {
      currentValue: ComputedRef<any>
      computedDisabled: ComputedRef<boolean>
      computedSize: ComputedRef<any>
      computedShowMessage: ComputedRef<boolean>
      isSearchLoading: ComputedRef<boolean> | any
      showTelControls: ComputedRef<boolean> | any
      telPrefix: ComputedRef<string> | any
    }
  ) {
    // 过滤容器属性
    const filteredProps = this.containerFilter(props)

    // 解包计算属性
    const unwrappedComputed = unwrapComputedValues(computedValues)

    // 合并属性
    return {
      ...filteredProps,
      value: unwrappedComputed.currentValue,
      name: props.name || '',
      disabled: unwrappedComputed.computedDisabled,
      loading: props.loading || unwrappedComputed.isSearchLoading,
      size: unwrappedComputed.computedSize,
      showMessage: unwrappedComputed.computedShowMessage,
      prefix: unwrappedComputed.showTelControls
        ? unwrappedComputed.telPrefix
        : props.prefix,
    }
  }
}

/**
 * 单例 InputField 属性处理器
 */
let inputFieldPropsProcessorInstance: InputFieldPropsProcessor | null = null

/**
 * 获取 InputField 属性处理器实例（单例模式）
 */
export function getInputFieldPropsProcessor() {
  if (!inputFieldPropsProcessorInstance) {
    inputFieldPropsProcessorInstance = new InputFieldPropsProcessor()
  }
  return inputFieldPropsProcessorInstance
}

/**
 * 创建 InputField 属性处理器实例（向后兼容）
 * @deprecated 使用 getInputFieldPropsProcessor() 获取单例实例
 */
export function createInputFieldPropsProcessor() {
  return getInputFieldPropsProcessor()
}

/**
 * 通用属性选择器 - 从对象中选择指定的属性
 */
export function pick<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}

/**
 * 通用属性排除器 - 从对象中排除指定的属性
 */
export function omit<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj } as any
  keys.forEach(key => {
    delete result[key]
  })
  return result
}

/**
 * 属性重命名器
 */
export function renameProps<T extends Record<string, any>>(
  obj: T,
  mapping: Record<string, string>
): Record<string, any> {
  const result: Record<string, any> = {}

  Object.entries(obj).forEach(([key, value]) => {
    const newKey = mapping[key] || key
    result[newKey] = value
  })

  return result
}
