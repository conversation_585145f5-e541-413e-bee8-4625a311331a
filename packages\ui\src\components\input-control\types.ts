/**
 * InputControl 组件类型定义
 * 从 FieldContainer 中抽离出来的控件布局相关类型
 */

/** InputControl 基础属性 */
export interface InputControlProps {
  /** 占位符文本 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 是否聚焦 */
  focused?: boolean
  /** 外观变体 */
  variant?: 'default' | 'underlined' | 'filled' | 'square' | 'unborder' | 'pill'
  /** 视觉效果 */
  effect?: 'none' | 'glow' | 'shadow'
  /** 组件尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 外部前置图标（位于输入框外部左侧） */
  prependIcon?: string
  /** 内部前置图标（位于输入框内部左侧） */
  prependIconInner?: string
  /** 外部后置图标（位于输入框外部右侧） */
  appendIcon?: string
  /** 内部后置图标（位于输入框内部右侧） */
  appendIconInner?: string
  /** 验证状态 */
  validateState?: 'error' | 'warning' | 'success' | ''
}

/** InputControl 事件 */
export interface InputControlEmits {
  /** 输入事件 */
  (e: 'input', event: Event): void
  /** 值改变事件 */
  (e: 'change', event: Event): void
  /** 获得焦点事件 */
  (e: 'focus', event: FocusEvent): void
  /** 失去焦点事件 */
  (e: 'blur', event: FocusEvent): void
  /** 键盘事件 */
  (e: 'keydown', event: KeyboardEvent): void
  /** 包装器点击事件 */
  (e: 'wrapper-click'): void
  /** 外部前置图标点击事件 */
  (e: 'click:prepend', event: MouseEvent): void
  /** 内部前置图标点击事件 */
  (e: 'click:prepend-inner', event: MouseEvent): void
  /** 外部后置图标点击事件 */
  (e: 'click:append', event: MouseEvent): void
  /** 内部后置图标点击事件 */
  (e: 'click:append-inner', event: MouseEvent): void
}
