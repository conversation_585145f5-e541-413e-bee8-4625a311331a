<!--
  FieldContainer.vue - 通用表单字段容器
  提供浮动标签、表单验证、统一样式等功能
  通过插槽支持任何类型的输入元素
-->

<template>
  <div
    :class="[rootClasses, sizeClass]"
    :style="dynamicSizeVars"
  >
    <!-- 整体容器：flex 布局包含外部图标和字段容器 -->
    <div class="sp-field-container__layout">
      <!-- 外部前置区域 -->
      <div
        v-if="shouldShowPrependOuter"
        class="sp-field-container__prepend-icon-outer"
      >
        <!-- 外部前置插槽 -->
        <slot
          name="prependOuter"
          :prepend-outer-classes="prependIconOuterClasses"
          :icon-size="iconSize"
        />
      </div>

      <!-- 字段容器 -->
      <div class="sp-field-container__field-wrapper">
        <!-- 字段包装器 -->
        <div
          ref="wrapperRef"
          :class="wrapperClasses"
          @click="handleWrapperClick"
        >
          <!-- 前置区域插槽 -->
          <slot
            name="prepend"
            :prepend-classes="prependClasses"
            :icon-size="iconSize"
            :prepend-icon-inner="prependIconInner"
            :on-prepend-inner-click="handlePrependInnerClick"
          />

          <!-- 输入容器（包含标签和输入框） -->
          <div class="sp-field-container__input-container">
            <!-- 浮动标签 -->
            <label
              v-if="label"
              :class="labelClasses"
              :for="fieldId"
              @click="handleLabelClick"
            >
              <span
                v-if="required"
                class="sp-field-container__label-asterisk"
              >
                *
              </span>
              {{ label }}
            </label>

            <!-- 输入元素插槽 -->
            <slot v-bind="{ ...fieldProps, ...inputProps, ...eventHandlers, ...prefixSuffixProps }" />
          </div>

          <!-- 后置区域插槽 -->
          <slot
            name="append"
            :append-classes="appendClasses"
            :icon-size="iconSize"
            :append-icon-inner="appendIconInner"
            :on-append-inner-click="handleAppendInnerClick"
          />

          <!-- 功能区域插槽（保持向后兼容） -->
          <slot
            name="functions"
            :functions-classes="functionsClasses"
            :icon-size="iconSize"
          />
        </div>

        <!-- 加载动画条 -->
        <div
          v-if="loading"
          :class="loadingBarClasses"
        >
          <div :class="loadingProgressClasses"></div>
        </div>

        <!-- 验证消息 -->
        <transition name="sp-field-container-message">
          <div
            v-if="shouldShowMessage"
            :class="messageClasses"
          >
            {{ currentMessage }}
          </div>
        </transition>

        <!-- 帮助文本 -->
        <div
          v-if="helperText && !shouldShowMessage"
          :class="helperTextClasses"
        >
          {{ helperText }}
        </div>
      </div>

      <!-- 外部后置区域 -->
      <div
        v-if="shouldShowAppendOuter"
        class="sp-field-container__append-icon-outer"
      >
        <!-- 外部后置插槽 -->
        <slot
          name="appendOuter"
          :append-outer-classes="appendIconOuterClasses"
          :icon-size="iconSize"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, useId, watch, ref, useSlots } from 'vue'
  import { useField } from 'vee-validate'
  import { useFieldContainerStyles } from './useFieldContainerStyles'
  import { useConfigurableSize } from '../../composables/useConfigurableSize'
  import type { FieldContainerProps, FieldContainerEmits } from './types'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<FieldContainerProps>(), {
    value: '',
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    error: false,
    warning: false,
    success: false,
    loading: false,
    required: false,
    showMessage: true,
    persistentLabel: false,
    prependIcon: undefined,
    prependIconInner: undefined,
    appendIcon: undefined,
    appendIconInner: undefined,
    prefix: undefined,
    suffix: undefined,
    showSuccessState: false,
    validateState: undefined,
    validateMessage: undefined,
  })

  const emit = defineEmits<FieldContainerEmits>()
  const slots = useSlots()

  // ===== 基础状态 =====
  const fieldId = useId()
  const wrapperRef = ref<HTMLDivElement>()
  const isFocused = ref(false)
  const hasValue = ref(false)

  // ===== 表单验证集成 =====
  const veeField = useField(props.name || '', props.rules as any, {
    validateOnValueUpdate: false,
  })

  // ===== 计算属性 =====
  const computedDisabled = computed(() => props.disabled)
  const isLabelFloating = computed(
    () => props.persistentLabel || isFocused.value || hasValue.value
  )
  const computedPlaceholder = computed(() =>
    isLabelFloating.value ? props.placeholder : ''
  )

  const iconSize = computed(() => {
    const sizeMap = {
      small: 16,
      medium: 18,
      large: 20,
    }
    return sizeMap[props.size] || 18
  })

  // ===== 外部图标样式工厂函数 =====
  const createIconOuterClasses = (position: 'prepend' | 'append') => {
    return computed(() => {
      const classes = [`sp-field-container__${position}-icon-outer-icon`]

      if (computedDisabled.value)
        classes.push(
          `sp-field-container__${position}-icon-outer-icon--disabled`
        )
      if (isFocused.value)
        classes.push(`sp-field-container__${position}-icon-outer-icon--focused`)
      if (validateState.value === 'error')
        classes.push(`sp-field-container__${position}-icon-outer-icon--error`)
      if (validateState.value === 'warning')
        classes.push(`sp-field-container__${position}-icon-outer-icon--warning`)
      if (validateState.value === 'success')
        classes.push(`sp-field-container__${position}-icon-outer-icon--success`)

      return classes.join(' ')
    })
  }

  // ===== 外部图标样式 =====
  const prependIconOuterClasses = createIconOuterClasses('prepend')
  const appendIconOuterClasses = createIconOuterClasses('append')

  // ===== 验证状态管理 =====
  const validateState = computed(() => {
    // 优先使用传入的 validateState
    if (props.validateState) return props.validateState

    // 检查 props 状态
    const propStates = [
      { condition: props.error, state: 'error' },
      { condition: props.warning, state: 'warning' },
      { condition: props.success, state: 'success' },
    ]

    for (const { condition, state } of propStates) {
      if (condition) return state
    }

    // 检查 VeeValidate 状态
    if (veeField?.meta.valid === false && veeField?.meta.touched) return 'error'

    return ''
  })

  const currentMessage = computed(() => {
    // 优先使用传入的 validateMessage
    if (props.validateMessage) return props.validateMessage
    return veeField?.errorMessage.value || ''
  })

  const shouldShowMessage = computed(() => {
    return props.showMessage && !!currentMessage.value
  })

  // ===== 事件处理 =====
  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement | HTMLTextAreaElement
    const value = target.value

    // 更新 hasValue 状态
    hasValue.value = !!value

    // 发出事件
    emit('update:value', value)
    emit('input', event)

    // 同步到 VeeValidate
    veeField?.setValue(value === '' ? undefined : value)
  }

  const handleChange = (event: Event) => {
    emit('change', event)
    // 触发验证
    veeField?.validate()
  }

  const handleFocus = (event: FocusEvent) => {
    isFocused.value = true
    emit('focus', event)
  }

  const handleBlur = async (event: FocusEvent) => {
    isFocused.value = false
    emit('blur', event)
    // 触发验证
    veeField?.handleBlur()
    await veeField?.validate()
  }

  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
  }

  const handleWrapperClick = () => {
    // 由具体的输入组件处理聚焦逻辑
    emit('wrapper-click')
  }

  const handleLabelClick = () => {
    if (!computedDisabled.value && !props.readonly) {
      emit('label-click')
    }
  }

  // ===== 图标点击事件工厂函数 =====
  const createIconClickHandler = (eventName: string) => {
    return (event: MouseEvent) => {
      if (!computedDisabled.value) {
        emit(eventName as any, event)
      }
    }
  }

  const handlePrependInnerClick = createIconClickHandler('click:prepend-inner')
  const handleAppendInnerClick = createIconClickHandler('click:append-inner')

  // ===== 插槽属性分组 =====
  const fieldProps = computed(() => ({
    fieldId: fieldId,
    disabled: computedDisabled.value,
    readonly: props.readonly,
  }))

  const inputProps = computed(() => ({
    inputClasses: inputClasses.value,
    inputStyle: inputStyle.value,
    placeholder: computedPlaceholder.value,
  }))

  const eventHandlers = computed(() => ({
    onFocus: handleFocus,
    onBlur: handleBlur,
    onInput: handleInput,
    onChange: handleChange,
    onKeydown: handleKeydown,
  }))

  // ===== 前缀后缀属性（传递给输入组件） =====
  const prefixSuffixProps = computed(() => ({
    prefix: props.prefix,
    suffix: props.suffix,
    prefixClasses: prefixClasses.value,
    suffixClasses: suffixClasses.value,
    shouldShowPrefix: shouldShowPrefix.value,
    shouldShowSuffix: shouldShowSuffix.value,
    isLabelFloating: isLabelFloating.value,
    iconSize: iconSize.value,
  }))

  // ===== 条件渲染辅助函数 =====
  const shouldShowPrefix = computed(
    () => (props.prefix || slots.prefix) && isLabelFloating.value
  )
  const shouldShowSuffix = computed(
    () => (props.suffix || slots.suffix) && isLabelFloating.value
  )
  const shouldShowPrependOuter = computed(() => slots.prependOuter)
  const shouldShowAppendOuter = computed(() => slots.appendOuter)

  // ===== 同步外部 value 到内部状态 =====
  watch(
    () => props.value,
    newValue => {
      hasValue.value = !!newValue
      if (veeField?.value.value !== newValue) {
        veeField?.setValue(newValue)
      }
    },
    { immediate: true }
  )

  // ===== 配置系统 =====
  const sizeRef = computed(() => props.size || 'medium')
  const { dynamicSizeVars, sizeClass } = useConfigurableSize(sizeRef)

  // ===== 样式系统 =====
  const {
    rootClasses,
    wrapperClasses,
    labelClasses,
    inputClasses,
    inputStyle,
    prependClasses,
    prefixClasses,
    suffixClasses,
    appendClasses,
    functionsClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  } = useFieldContainerStyles(props, {
    computedDisabled,
    isFocused,
    hasValue,
    isLabelFloating,
    validateState,
  })

  // ===== 表单方法 =====
  const validate = async (): Promise<boolean> => {
    const result = await veeField?.validate()
    return result?.valid || false
  }

  const resetField = () => {
    veeField?.resetField()
    emit('update:value', undefined)
    hasValue.value = false
  }

  const clearValidate = () => {
    veeField?.setErrors([])
  }

  // ===== 暴露方法和状态 =====
  defineExpose({
    validate,
    resetField,
    clearValidate,
    get wrapper() {
      return wrapperRef.value || null
    },
    // 暴露状态供子组件使用
    isFocused: computed(() => isFocused.value),
    hasValue: computed(() => hasValue.value),
    isLabelFloating,
    validateState,
  })
</script>

<script lang="ts">
  export default {
    name: 'FieldContainer',
  }
</script>
