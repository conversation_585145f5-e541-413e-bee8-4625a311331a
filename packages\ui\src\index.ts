import type { App } from 'vue'
import Button from './components/button'
import Row from './components/Row'
import Col from './components/Col'
// import Input, {
//   Password,
//   Email,
//   Search,
//   TextArea,
//   Number,
// } from './components/input'
import Input from './components/input'
import InputField from './components/input-field'
import SelectField from './components/select-field'
import Textarea from './components/textarea'
import TextareaField from './components/textarea-field'
import FieldContainer from './components/field-container'
import Icon from './components/icon'
import Form from './components/Form'
import { FormItem } from './components/Form'
import Select, { SelectOption } from './components/select'
import Switch from './components/switch'
import Tag from './components/tag'
import { Menu, MenuItem } from './components/menu'
import { List, ListItem } from './components/list'
import { Dropdown } from './components/dropdown'
import Scrollbar from './components/scrollbar'
import Slider from './components/slider'
import SliderField from './components/slider-field'
// import ThemeSwitcher from './components/theme-switcher/theme-switcher.vue'
// import ConfigProvider from './components/config-provider'
import { installDirectives, vLinkage } from './directives'

const components = [
  { component: Button, name: 'SpButton', kebabName: 'sp-button' },
  { component: Row, name: 'SpRow', kebabName: 'sp-row' },
  { component: Col, name: 'SpCol', kebabName: 'sp-col' },
  // { component: Input, name: 'SpInput', kebabName: 'sp-input' },
  { component: Input, name: 'SpInput', kebabName: 'sp-input' },
  { component: InputField, name: 'SpInputField', kebabName: 'sp-input-field' },
  {
    component: SelectField,
    name: 'SpSelectField',
    kebabName: 'sp-select-field',
  },
  { component: Textarea, name: 'SpTextarea', kebabName: 'sp-textarea' },
  {
    component: TextareaField,
    name: 'SpTextareaField',
    kebabName: 'sp-textarea-field',
  },
  {
    component: FieldContainer,
    name: 'SpFieldContainer',
    kebabName: 'sp-field-container',
  },
  // {
  //   component: Password,
  //   name: 'SpInputPassword',
  //   kebabName: 'sp-input-password',
  // },
  // {
  //   component: Email,
  //   name: 'SpInputEmail',
  //   kebabName: 'sp-input-email',
  // },
  // { component: Search, name: 'SpInputSearch', kebabName: 'sp-input-search' },
  // { component: TextArea, name: 'SpTextArea', kebabName: 'sp-textarea' },
  { component: Icon, name: 'SpIcon', kebabName: 'sp-icon' },
  { component: Form, name: 'SpForm', kebabName: 'sp-form' },
  { component: FormItem, name: 'SpFormItem', kebabName: 'sp-form-item' },
  { component: Select, name: 'SpSelect', kebabName: 'sp-select' },
  {
    component: SelectOption,
    name: 'SpSelectOption',
    kebabName: 'sp-select-option',
  },
  // {
  //   component: Multiple,
  //   name: 'SpSelectMultiple',
  //   kebabName: 'sp-select-multiple',
  // },
  { component: Switch, name: 'SpSwitch', kebabName: 'sp-switch' },
  { component: Tag, name: 'SpTag', kebabName: 'sp-tag' },
  { component: Menu, name: 'SpMenu', kebabName: 'sp-menu' },
  { component: MenuItem, name: 'SpMenuItem', kebabName: 'sp-menu-item' },
  { component: List, name: 'SpList', kebabName: 'sp-list' },
  { component: ListItem, name: 'SpListItem', kebabName: 'sp-list-item' },
  { component: Dropdown, name: 'SpDropdown', kebabName: 'sp-dropdown' },
  { component: Scrollbar, name: 'SpScrollbar', kebabName: 'sp-scrollbar' },
  { component: Slider, name: 'SpSlider', kebabName: 'sp-slider' },
  {
    component: SliderField,
    name: 'SpSliderField',
    kebabName: 'sp-slider-field',
  },
  // {
  //   component: ThemeSwitcher,
  //   name: 'SpThemeSwitcher',
  //   kebabName: 'sp-theme-switcher',
  // },
  // {
  //   component: ConfigProvider,
  //   name: 'SpConfigProvider',
  //   kebabName: 'sp-config-provider',
  // },
]

const install = (app: App): void => {
  components.forEach(({ component, name, kebabName }) => {
    // 注册大写形式
    app.component(name, component)
    // 注册小写形式
    app.component(kebabName, component)
  })

  // 安装指令
  installDirectives(app)
}

export {
  Button,
  Row,
  Col,
  Input,
  InputField,
  SelectField,
  Textarea,
  TextareaField,
  // Input,
  // Password,
  // Email,
  // Search,
  // TextArea,
  Icon,
  Form,
  FormItem,
  Select,
  SelectOption,
  // Multiple,
  Switch,
  Tag,
  Menu,
  MenuItem,
  List,
  ListItem,
  Dropdown,
  Scrollbar,
  Slider,
  SliderField,
  vLinkage,
}

// 导出 Menu 组件相关
export * from './components/menu'

// 导出 Dropdown 组件相关
export * from './components/dropdown'

// 导出 Select 组件相关
export * from './components/select'

// 导出 SelectField 组件相关
export * from './components/select-field'

// 导出 TextareaField 组件相关
export * from './components/textarea-field'

// 导出 Tag 组件相关
export * from './components/tag'

// 导出 Slider 组件相关
export * from './components/slider'

// 导出 SliderField 组件相关
export * from './components/slider-field'

// 重新导出主题相关的 hooks（为了用户便利）
export {
  useTheme,
  setTheme,
  setPrimaryColor,
  resetTheme,
  PRESET_THEMES,
  type ThemeConfig,
  type PresetThemeName,
} from '@speed-ui/hooks'

export default {
  install,
}
