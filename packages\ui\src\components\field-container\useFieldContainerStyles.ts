import { computed, type Ref } from 'vue'
import { useBEM } from '@speed-ui/bem-helper'
import type { FieldContainerProps } from './types'

interface FieldContainerLogicState {
  computedDisabled: Ref<boolean>
  isFocused: Ref<boolean>
  hasValue: Ref<boolean>
  isLabelFloating: Ref<boolean>
  validateState: Ref<string>
}

export function useFieldContainerStyles(
  props: FieldContainerProps,
  logicState: FieldContainerLogicState
) {
  const bem = useBEM('field-container')

  // ===== 根容器类名 =====
  const rootClasses = computed(() => {
    const classes = [bem.b()]

    // 尺寸类名 - 只有非默认值时才添加
    const size = props.size || 'medium'
    if (size !== 'medium') {
      classes.push(bem.m(size))
    }

    // 变体类名 - 总是添加，因为CSS样式依赖于变体类名
    const variant = props.variant || 'default'
    classes.push(bem.m(variant))

    // 效果类名 - 只有非默认值时才添加
    const effect = props.effect || 'none'
    if (effect !== 'none') {
      classes.push(bem.m(`effect-${effect}`))
    }

    // 条件类名 - 只有为 true 时才添加
    const conditionalClasses = {
      [bem.m('disabled')]: logicState.computedDisabled.value,
      [bem.m('readonly')]: props.readonly,
      [bem.m('focused')]: logicState.isFocused.value,
      [bem.m('error')]:
        props.error || logicState.validateState.value === 'error',
      [bem.m('warning')]:
        props.warning || logicState.validateState.value === 'warning',
      [bem.m('success')]:
        props.success || logicState.validateState.value === 'success',
      [bem.m('loading')]: props.loading,
      [bem.m('has-value')]: logicState.hasValue.value,
      [bem.m('label-floating')]: logicState.isLabelFloating.value,
      [bem.m('persistent-label')]: props.persistentLabel,
      [bem.m('has-prefix')]: !!props.prefix,
      [bem.m('prefix-floating')]:
        !!props.prefix && logicState.isLabelFloating.value,
      [bem.m('has-suffix')]: !!props.suffix,
      [bem.m('suffix-floating')]:
        !!props.suffix && logicState.isLabelFloating.value,
    }

    return [...classes, conditionalClasses]
  })

  // ===== 各部分类名 =====
  const wrapperClasses = computed(() => [bem.e('wrapper')])

  const labelClasses = computed(() => [
    bem.e('label'),
    {
      [bem.em('label', 'floating')]: logicState.isLabelFloating.value,
      [bem.em('label', 'required')]: props.required,
    },
  ])

  const inputClasses = computed(() => [bem.e('input')])

  const inputStyle = computed(() => ({}))

  const prependClasses = computed(() => [bem.e('prepend')])

  const prefixClasses = computed(() => [bem.e('prefix')])

  const suffixClasses = computed(() => [bem.e('suffix')])

  const appendClasses = computed(() => [bem.e('append')])

  const functionsClasses = computed(() => [bem.e('functions')])

  const loadingBarClasses = computed(() => [bem.e('loading-bar')])

  const loadingProgressClasses = computed(() => [bem.e('loading-progress')])

  const helperTextClasses = computed(() => [
    bem.e('helper'),
    {
      [bem.em('helper', 'error')]:
        props.error || logicState.validateState.value === 'error',
      [bem.em('helper', 'warning')]:
        logicState.validateState.value === 'warning',
      [bem.em('helper', 'success')]:
        logicState.validateState.value === 'success',
    },
  ])

  const messageClasses = computed(() => {
    const classes = [bem.e('message')]

    // 只有存在验证状态时才添加状态类名
    const state = logicState.validateState.value
    if (state && state !== '') {
      classes.push(bem.em('message', state))
    }

    return classes
  })

  return {
    // 类名
    rootClasses,
    wrapperClasses,
    labelClasses,
    inputClasses,
    inputStyle,
    prependClasses,
    prefixClasses,
    suffixClasses,
    appendClasses,
    functionsClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  }
}
