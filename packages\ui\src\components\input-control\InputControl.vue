<!--
  InputControl.vue - 输入控件布局组件
-->

<template>
  <div class="sp-field-container__layout">
    <!-- 外部前置区域 -->
    <div
      v-if="$slots.prependOuter"
      class="sp-field-container__prepend-icon-outer"
    >
      <slot name="prependOuter" />
    </div>

    <!-- 字段容器 -->
    <div class="sp-field-container__field-wrapper">
      <div class="sp-field-container__wrapper">
        <!-- 内部前置插槽 -->
        <slot name="prepend" />

        <div class="sp-field-container__input-container">
          <!-- 前缀插槽 -->
          <slot
            name="prefix"
            :prefix-classes="prefixClasses"
            :prefix="prefix"
          >
            <!-- 默认显示前缀内容 -->
            <span v-if="prefix">
              {{ prefix }}
            </span>
          </slot>

          <!-- 输入元素 -->
          <BaseInput
            ref="inputRef"
            v-model:value="currentValue"
            :placeholder="placeholder"
            :disabled="disabled"
            :readonly="readonly"
          />

          <!-- 后缀插槽 -->
          <slot
            name="suffix"
            :suffix-classes="suffixClasses"
            :suffix="suffix"
          >
            <!-- 默认显示后缀内容 -->
            <span v-if="suffix">
              {{ suffix }}
            </span>
          </slot>
        </div>

        <!-- 内部后置插槽 -->
        <slot name="append" />
      </div>
    </div>

    <!-- 外部后置区域 -->
    <div
      v-if="$slots.appendOuter"
      class="sp-field-container__append-icon-outer"
    >
      <slot name="appendOuter" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, useId } from 'vue'
  import { useFieldContainerStyles } from '../field-container/useFieldContainerStyles'
  import { useConfigurableSize } from '../../composables/useConfigurableSize'
  import BaseInput from '../baseinput/baseinput'
  import type { InputControlProps, InputControlEmits } from './types'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<InputControlProps>(), {
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    loading: false,
    prefix: '',
    suffix: '',
  })

  const emit = defineEmits<InputControlEmits>()

  // ===== 基础状态 =====
  const fieldId = useId()
  const wrapperRef = ref<HTMLDivElement>()
  const inputRef = ref()
  const currentValue = ref('')

  // ===== 逻辑状态（简化版本） =====
  const computedDisabled = computed(() => props.disabled)
  const isFocused = computed(() => props.focused || false)
  const hasValue = computed(() => false) // 简化版本，暂时不处理值
  const isLabelFloating = computed(() => false) // 简化版本，没有标签
  const validateState = computed(() => props.validateState || '')

  // ===== 尺寸系统 =====
  const sizeRef = computed(() => props.size)
  const { dynamicSizeVars, sizeClass } = useConfigurableSize(sizeRef)

  // ===== 样式系统（直接使用 FieldContainer 的样式系统） =====
  const {
    rootClasses,
    wrapperClasses,
    inputClasses,
    inputStyle,
    prependClasses,
    prefixClasses,
    suffixClasses,
    appendClasses,
    loadingBarClasses,
    loadingProgressClasses,
  } = useFieldContainerStyles(props, {
    computedDisabled,
    isFocused,
    hasValue,
    isLabelFloating,
    validateState,
  })

  // ===== 事件处理 =====
  const handleInput = (event: Event) => {
    emit('input', event)
  }

  const handleChange = (event: Event) => {
    emit('change', event)
  }

  const handleFocus = (event: FocusEvent) => {
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    emit('blur', event)
  }

  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
  }

  const handleWrapperClick = () => {
    emit('wrapper-click')
  }
</script>
