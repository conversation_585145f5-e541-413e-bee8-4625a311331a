/**
 * Input 组件类型定义
 */

/** 输入框尺寸 - 支持预设值或自定义数字 */
export type InputSize = 'large' | 'medium' | 'small' | number

/** 输入框类型 */
export type InputType =
  | 'text'
  | 'password'
  | 'email'
  | 'number'
  | 'tel'
  | 'url'
  | 'search'
  | 'smscode'

/** 输入框变体 */
export type InputVariant =
  | 'default'
  | 'underlined'
  | 'filled'
  | 'pill'
  | 'square'
  | 'unborder'

/** 输入框效果类型 */
export type InputEffect = 'none' | 'glow' | 'shadow'

/** 验证状态 */
export type ValidateState = 'success' | 'warning' | 'error'

/** 输入框模式 */
export type InputMode = 'input' | 'select' | 'display'

/** 输入框属性 */
export interface InputProps {
  /** 绑定值 - 使用 v-model:value */
  value?: string | number
  /** 输入框类型 */
  type?: InputType
  /** 输入框变体 */
  variant?: InputVariant
  /** 视觉效果 */
  effect?: InputEffect
  /** 尺寸 */
  size?: InputSize
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否显示清除按钮 */
  clearable?: boolean
  /** 是否显示密码切换按钮 */
  showPassword?: boolean
  /** 自定义密码显示图标 */
  passwordShowIcon?: string
  /** 自定义密码隐藏图标 */
  passwordHideIcon?: string
  /** 外部前置图标名称（位于输入框外部左侧） */
  prependIcon?: string
  /** 内部前置图标名称（位于输入框内部左侧） */
  prependIconInner?: string
  /** 外部后置图标名称（位于输入框外部右侧） */
  appendIcon?: string
  /** 内部后置图标名称（位于输入框内部右侧） */
  appendIconInner?: string
  /** 最大输入长度 */
  maxlength?: number
  /** 最小输入长度 */
  minlength?: number
  /** 是否显示字数统计 */
  showWordLimit?: boolean
  /** 是否显示错误状态 */
  error?: boolean
  /** 是否显示警告状态 */
  warning?: boolean
  /** 是否显示成功状态 */
  success?: boolean
  /** 验证状态 */
  validateState?: ValidateState
  /** 验证消息 */
  validateMessage?: string
  /** 是否显示加载动画 */
  loading?: boolean
  /** 自动完成 */
  autocomplete?: string
  /** 自动聚焦 */
  autofocus?: boolean
  /** 输入模式 */
  inputmode?:
    | 'none'
    | 'text'
    | 'tel'
    | 'url'
    | 'email'
    | 'numeric'
    | 'decimal'
    | 'search'
    | 'smscode'
  /** 步长（用于数字输入） */
  step?: number
  /** 最小值（用于数字输入） */
  min?: number
  /** 最大值（用于数字输入） */
  max?: number
  /** 是否必填 */
  required?: boolean
  /** 名称（用于表单） */
  name?: string
  /** 模式（内部使用） */
  mode?: InputMode
}

/** 输入框事件 */
export interface InputEmits {
  /** 值更新事件 - 用于 v-model:value */
  (e: 'update:value', value: string | number | undefined): void
  /** 焦点状态更新事件 */
  (e: 'update:focused', focused: boolean): void
  /** 值改变事件 */
  (e: 'change', value: string | number | undefined): void
  /** 输入事件 */
  (e: 'input', event: Event): void
  /** 获得焦点事件 */
  (e: 'focus', event: FocusEvent): void
  /** 失去焦点事件 */
  (e: 'blur', event: FocusEvent): void
  /** 键盘按下事件 */
  (e: 'keydown', event: KeyboardEvent): void
  /** 键盘抬起事件 */
  (e: 'keyup', event: KeyboardEvent): void
  /** 键盘按下事件 */
  (e: 'keypress', event: KeyboardEvent): void
  /** 清除事件 */
  (e: 'clear'): void
  /** 点击事件 */
  (e: 'click', event: MouseEvent): void
  /** 回车事件 */
  (e: 'enter', event: KeyboardEvent): void
  /** 搜索事件 */
  (e: 'search'): void
}

/** 输入框实例方法 */
export interface InputInstance {
  /** 使输入框获得焦点 */
  focus: () => void
  /** 使输入框失去焦点 */
  blur: () => void
  /** 选中输入框中的文字 */
  select: () => void
  /** 清空输入框 */
  clear: () => void
  /** 输入框元素引用 */
  input: HTMLInputElement | null
  /** 包装器元素引用 */
  wrapper: HTMLDivElement | null
}

/** 输入框默认属性 */
export const inputPropsDefaults: Partial<InputProps> = {
  value: '',
  type: 'text',
  variant: 'default',
  effect: 'none',
  size: 'medium',
  disabled: false,
  readonly: false,
  clearable: false,
  showPassword: false,
  showWordLimit: false,
  error: false,
  warning: false,
  success: false,
  loading: false,
  mode: 'input',
} as const

/** 输入框插槽 */
export interface InputSlots {
  /** 默认插槽 */
  default?: () => any
  /** 前缀插槽 */
  prefix?: () => any
  /** 后缀插槽 */
  suffix?: () => any
  /** 内部内容插槽（用于 Select 等组件） */
  inner?: () => any
  /** 验证消息插槽 */
  message?: () => any
}
