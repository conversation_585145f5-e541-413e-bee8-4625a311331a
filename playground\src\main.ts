import { createApp } from 'vue'
import SpeedUI from '@speed-ui/ui'
import { i18n } from './i18n'
import { router } from './router'
import App from './App.vue'

import './style.css'
// 导入Speed UI默认主题样式（已包含尺寸预设）
import '@speed-ui/theme-default/src/index.scss'

// 导入配置系统
import { setSpeedUIConfig } from '../../packages/ui/src/config/speedConfig'
import {
  setSpeedUIThemeConfig,
  applyTheme,
} from '../../packages/ui/src/config/speedThemeConfig'

// 自定义全局尺寸配置
setSpeedUIConfig({
  // small 对应 30px
  small: {
    base: 30, // 基础高度 30px
    fontSize: 13, // 字体大小按比例计算：(30/48)*16 ≈ 10，但设为 13 更合适
    paddingTop: 15, // 上边距按比例：(30/48)*24 = 15
    paddingBottom: 3, // 下边距按比例：(30/48)*4 = 2.5，设为 3
    labelFloatingTop: 5, // 浮动标签位置：(30/48)*8 = 5
    labelFontSize: 11, // 标签字体：(30/48)*14 ≈ 9，但设为 11 更合适
    prefixPadding: 3, // 右侧间距：(30/48)*4 = 2.5，设为 3
    prefixPaddingLeft: 10, // 左侧间距：(30/48)*16 = 10
    prefixPaddingDefault: 13, // 默认变体：(30/48)*20 = 12.5，设为 13
    prefixPaddingSquare: 3,
    gap: 8, // 间距：(30/48)*12 = 7.5，设为 8
  },

  // medium 对应 48px（保持默认）
  medium: {
    base: 48, // 基础高度 48px
    fontSize: 16, // 字体大小 16px
    paddingTop: 24, // 上边距 24px
    paddingBottom: 4, // 下边距 4px
    labelFloatingTop: 8,
    labelFontSize: 14,
    prefixPadding: 4,
    prefixPaddingLeft: 16,
    prefixPaddingDefault: 20,
    prefixPaddingSquare: 4,
    gap: 12,
  },

  // large 对应 75px
  large: {
    base: 75, // 基础高度 75px
    fontSize: 25, // 字体大小按比例：(75/48)*16 = 25
    paddingTop: 38, // 上边距按比例：(75/48)*24 = 37.5，设为 38
    paddingBottom: 6, // 下边距按比例：(75/48)*4 = 6.25，设为 6
    labelFloatingTop: 13, // 浮动标签位置：(75/48)*8 = 12.5，设为 13
    labelFontSize: 22, // 标签字体：(75/48)*14 = 21.875，设为 22
    prefixPadding: 6, // 右侧间距：(75/48)*4 = 6.25，设为 6
    prefixPaddingLeft: 25, // 左侧间距：(75/48)*16 = 25
    prefixPaddingDefault: 31, // 默认变体：(75/48)*20 = 31.25，设为 31
    prefixPaddingSquare: 6,
    gap: 19, // 间距：(75/48)*12 = 18.75，设为 19
  },

  // 自定义数字尺寸的映射函数
  customSizeMapper: (size: number) => {
    // 改变基准比例，让数字尺寸更加舒适
    const ratio = size / 50 // 基准从 48 改为 50
    return {
      base: size,
      fontSize: Math.round(18 * ratio), // 基础字体从 16 改为 18
      paddingTop: Math.round(28 * ratio), // 基础上边距从 24 改为 28
      paddingBottom: Math.round(6 * ratio), // 基础下边距从 4 改为 6
      labelFloatingTop: Math.round(10 * ratio),
      labelFontSize: Math.round(16 * ratio),
      prefixPadding: Math.round(6 * ratio),
      prefixPaddingLeft: Math.round(20 * ratio), // 填充变体左边距更大
      prefixPaddingDefault: Math.round(24 * ratio), // 默认变体更大
      prefixPaddingSquare: Math.round(6 * ratio),
      gap: Math.round(15 * ratio),
    }
  },
})

// 自定义主题配置演示
setSpeedUIThemeConfig({
  // 自定义绿色主题
  green: {
    primary: '#10b981', // 更现代的绿色
    primaryHover: '#34d399',
    primaryActive: '#059669',
    primaryDisabled: '#86efac',
    primaryLightest: '#d1fae5',
    primaryLight: '#a7f3d0',
    primaryDark: '#047857',
  },

  // 自定义紫色主题
  purple: {
    primary: '#8b5cf6', // 更现代的紫色
    primaryHover: '#a78bfa',
    primaryActive: '#7c3aed',
    primaryDisabled: '#c4b5fd',
    primaryLightest: '#f5f3ff',
    primaryLight: '#ede9fe',
    primaryDark: '#6d28d9',
  },

  // 自定义粉色主题
  pink: {
    primary: '#ec4899', // 现代粉色
    primaryHover: '#f472b6',
    primaryActive: '#db2777',
    primaryDisabled: '#f9a8d4',
    primaryLightest: '#fdf2f8',
    primaryLight: '#fce7f3',
    primaryDark: '#be185d',
  },

  // 自定义主题映射函数
  customThemeMapper: (primaryColor: string) => {
    // 你可以在这里自定义颜色算法
    // 比如使用HSL颜色空间进行更精确的颜色计算
    return {
      primary: primaryColor,
      primaryHover: adjustColor(primaryColor, 10, 'lighten'),
      primaryActive: adjustColor(primaryColor, 10, 'darken'),
      primaryDisabled: adjustColor(primaryColor, 40, 'lighten'),
      primaryLightest: adjustColor(primaryColor, 50, 'lighten'),
      primaryLight: adjustColor(primaryColor, 30, 'lighten'),
      primaryDark: adjustColor(primaryColor, 20, 'darken'),
    }
  },
})

// 颜色调整函数
function adjustColor(
  color: string,
  amount: number,
  type: 'lighten' | 'darken'
): string {
  const hex = color.replace('#', '')
  const num = parseInt(hex, 16)
  const r = (num >> 16) & 255
  const g = (num >> 8) & 255
  const b = num & 255

  if (type === 'lighten') {
    const newR = Math.min(255, Math.round(r + ((255 - r) * amount) / 100))
    const newG = Math.min(255, Math.round(g + ((255 - g) * amount) / 100))
    const newB = Math.min(255, Math.round(b + ((255 - b) * amount) / 100))
    return (
      '#' + ((newR << 16) | (newG << 8) | newB).toString(16).padStart(6, '0')
    )
  } else {
    const newR = Math.max(0, Math.round(r * (1 - amount / 100)))
    const newG = Math.max(0, Math.round(g * (1 - amount / 100)))
    const newB = Math.max(0, Math.round(b * (1 - amount / 100)))
    return (
      '#' + ((newR << 16) | (newG << 8) | newB).toString(16).padStart(6, '0')
    )
  }
}

// 应用主题（可以是预设主题名称或自定义颜色）
// applyTheme('blue') // 使用默认蓝色主题
// applyTheme('green')        // 使用预设绿色主题
// applyTheme('purple')       // 使用预设紫色主题
applyTheme('pink') // 使用预设粉色主题
// applyTheme('#ff6b6b')      // 使用自定义颜色（会自动生成主题）

const app = createApp(App)

app.use(SpeedUI)
app.use(i18n)
app.use(router)

// 同步路由参数和 i18n 语言
router.afterEach(to => {
  const locale = to.params.locale as string
  if (locale && locale !== i18n.global.locale.value) {
    i18n.global.locale.value = locale as any
  }
})

app.mount('#app')
