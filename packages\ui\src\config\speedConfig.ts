/**
 * Speed UI 全局配置
 */

// 基础尺寸配置
export interface SizeConfig {
  /** 基础尺寸 */
  base: number
  /** 字体大小 */
  fontSize: number
  /** 上边距 */
  paddingTop: number
  /** 下边距 */
  paddingBottom: number
  /** 标签浮动位置 */
  labelFloatingTop: number
  /** 标签字体大小 */
  labelFontSize: number
  /** 前缀右侧间距 */
  prefixPadding: number
  /** 前缀左侧间距（填充变体） */
  prefixPaddingLeft: number
  /** 前缀间距（默认变体） */
  prefixPaddingDefault: number
  /** 前缀间距（方形变体） */
  prefixPaddingSquare: number
  /** 元素间距 */
  gap: number
}

// 预设尺寸配置
export interface SpeedUIConfig {
  /** 小尺寸配置 */
  small: SizeConfig
  /** 中等尺寸配置 */
  medium: SizeConfig
  /** 大尺寸配置 */
  large: SizeConfig
  /** 超大尺寸配置 */
  xlarge: SizeConfig
  /** 自定义尺寸映射函数 */
  customSizeMapper?: (size: number) => SizeConfig
}

// 默认配置
export const defaultSpeedUIConfig: SpeedUIConfig = {
  small: {
    base: 36,
    fontSize: 14,
    paddingTop: 18,
    paddingBottom: 3,
    labelFloatingTop: 6,
    labelFontSize: 12,
    prefixPadding: 3,
    prefixPaddingLeft: 12,
    prefixPaddingDefault: 15,
    prefixPaddingSquare: 3,
    gap: 9,
  },
  medium: {
    base: 48,
    fontSize: 16,
    paddingTop: 24,
    paddingBottom: 4,
    labelFloatingTop: 8,
    labelFontSize: 14,
    prefixPadding: 4,
    prefixPaddingLeft: 16,
    prefixPaddingDefault: 20,
    prefixPaddingSquare: 4,
    gap: 12,
  },
  large: {
    base: 56,
    fontSize: 18,
    paddingTop: 28,
    paddingBottom: 4,
    labelFloatingTop: 10,
    labelFontSize: 16,
    prefixPadding: 6,
    prefixPaddingLeft: 20,
    prefixPaddingDefault: 25,
    prefixPaddingSquare: 6,
    gap: 14,
  },
  xlarge: {
    base: 64,
    fontSize: 20,
    paddingTop: 32,
    paddingBottom: 6,
    labelFloatingTop: 12,
    labelFontSize: 18,
    prefixPadding: 8,
    prefixPaddingLeft: 24,
    prefixPaddingDefault: 30,
    prefixPaddingSquare: 8,
    gap: 16,
  },
  // 自定义尺寸映射函数（用于数字尺寸）
  customSizeMapper: (size: number): SizeConfig => {
    const ratio = size / 48 // 以 medium 为基准
    return {
      base: size,
      fontSize: Math.round(16 * ratio),
      paddingTop: Math.round(24 * ratio),
      paddingBottom: Math.round(4 * ratio),
      labelFloatingTop: Math.round(8 * ratio),
      labelFontSize: Math.round(14 * ratio),
      prefixPadding: Math.round(4 * ratio),
      prefixPaddingLeft: Math.round(16 * ratio),
      prefixPaddingDefault: Math.round(20 * ratio),
      prefixPaddingSquare: Math.round(4 * ratio),
      gap: Math.round(12 * ratio),
    }
  },
}

// 全局配置实例
let globalConfig: SpeedUIConfig = { ...defaultSpeedUIConfig }

/**
 * 设置全局配置
 */
export function setSpeedUIConfig(config: Partial<SpeedUIConfig>) {
  globalConfig = {
    ...globalConfig,
    ...config,
  }
  // 更新 CSS 变量
  updateCSSVariables()
}

/**
 * 获取全局配置
 */
export function getSpeedUIConfig(): SpeedUIConfig {
  return globalConfig
}

/**
 * 根据尺寸获取配置
 */
export function getSizeConfig(size: string | number): SizeConfig {
  if (typeof size === 'string') {
    return globalConfig[size as keyof SpeedUIConfig] as SizeConfig || globalConfig.medium
  }
  
  if (typeof size === 'number') {
    return globalConfig.customSizeMapper?.(size) || globalConfig.medium
  }
  
  return globalConfig.medium
}

/**
 * 更新全局 CSS 变量
 */
function updateCSSVariables() {
  const root = document.documentElement
  
  // 为每个预设尺寸生成 CSS 变量
  Object.entries(globalConfig).forEach(([sizeName, config]) => {
    if (typeof config === 'object' && 'base' in config) {
      const sizeConfig = config as SizeConfig
      
      root.style.setProperty(`--sp-size-${sizeName}-base`, `${sizeConfig.base}px`)
      root.style.setProperty(`--sp-size-${sizeName}-font-size`, `${sizeConfig.fontSize}px`)
      root.style.setProperty(`--sp-size-${sizeName}-padding-top`, `${sizeConfig.paddingTop}px`)
      root.style.setProperty(`--sp-size-${sizeName}-padding-bottom`, `${sizeConfig.paddingBottom}px`)
      root.style.setProperty(`--sp-size-${sizeName}-label-floating-top`, `${sizeConfig.labelFloatingTop}px`)
      root.style.setProperty(`--sp-size-${sizeName}-label-font-size`, `${sizeConfig.labelFontSize}px`)
      root.style.setProperty(`--sp-size-${sizeName}-prefix-padding`, `${sizeConfig.prefixPadding}px`)
      root.style.setProperty(`--sp-size-${sizeName}-prefix-padding-left`, `${sizeConfig.prefixPaddingLeft}px`)
      root.style.setProperty(`--sp-size-${sizeName}-prefix-padding-default`, `${sizeConfig.prefixPaddingDefault}px`)
      root.style.setProperty(`--sp-size-${sizeName}-prefix-padding-square`, `${sizeConfig.prefixPaddingSquare}px`)
      root.style.setProperty(`--sp-size-${sizeName}-gap`, `${sizeConfig.gap}px`)
    }
  })
}

// 初始化时更新 CSS 变量
if (typeof document !== 'undefined') {
  updateCSSVariables()
}
