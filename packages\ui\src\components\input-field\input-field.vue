<!--
  InputField.vue - 优化版本
  使用更简洁的模板语法和事件处理
-->

<template>
  <FieldContainer
    ref="fieldContainerRef"
    v-bind="containerProps"
    :style="dynamicSizeVars"
    v-on="containerEventHandlers"
  >
    <!-- 外部前置区域 -->
    <template
      #prependOuter="slotProps"
      v-if="hasPrependOuterControls"
    >
      <div :class="slotProps.prependOuterClasses">
        <ControlRenderer
          position="prependOuter"
          :context="createControlContext(slotProps)"
          @control-event="handleControlEvent"
        />
        <slot name="prependOuter" />
      </div>
    </template>

    <!-- 前置区域 -->
    <template
      #prepend="slotProps"
      v-if="hasPrependControls"
    >
      <div :class="slotProps.prependClasses">
        <ControlRenderer
          position="prepend"
          :context="createControlContext(slotProps)"
          @control-event="handleControlEvent"
        />
        <slot name="prepend" />
      </div>
    </template>

    <!-- 输入元素 -->
    <template #default="slotProps">
      <InputWithPrefixSuffix
        ref="inputRef"
        v-model:value="currentValue"
        v-bind="baseInputProps(slotProps)"
        v-on="inputEventHandlers(slotProps)"
        :prefix="slotProps.prefix"
        :suffix="slotProps.suffix"
        :prefix-classes="slotProps.prefixClasses"
        :suffix-classes="slotProps.suffixClasses"
        :should-show-prefix="slotProps.shouldShowPrefix"
        :should-show-suffix="slotProps.shouldShowSuffix"
        :is-label-floating="slotProps.isLabelFloating"
        :icon-size="slotProps.iconSize"
      >
        <!-- 传递前缀插槽 -->
        <template #prefix="prefixSlotProps">
          <slot name="prefix" v-bind="prefixSlotProps" />
        </template>

        <!-- 传递后缀插槽 -->
        <template #suffix="suffixSlotProps">
          <slot name="suffix" v-bind="suffixSlotProps" />
        </template>
      </InputWithPrefixSuffix>
    </template>

    <!-- 后置区域 -->
    <template
      #append="slotProps"
      v-if="hasAppendControls"
    >
      <div :class="slotProps.appendClasses">
        <ControlRenderer
          position="append"
          :context="createControlContext(slotProps)"
          @control-event="handleControlEvent"
        />
        <slot name="append" />
      </div>
    </template>

    <!-- 外部后置区域 -->
    <template
      #appendOuter="slotProps"
      v-if="hasAppendOuterControls"
    >
      <div :class="slotProps.appendOuterClasses">
        <ControlRenderer
          position="appendOuter"
          :context="createControlContext(slotProps)"
          @control-event="handleControlEvent"
        />
        <slot name="appendOuter" />
      </div>
    </template>
  </FieldContainer>
</template>

<script setup lang="ts">
  import { computed, ref, inject, onMounted, onUnmounted } from 'vue'
  import FieldContainer from '../field-container/FieldContainer.vue'
  import BaseInput from '../baseinput/baseinput'
  import InputWithPrefixSuffix from './InputWithPrefixSuffix.vue'
  import { ControlRenderer } from './controls'
  import { useInputLogic } from '../../composables'
  import { useInputControls } from './composables/useInputControls'
  import { getInputFieldPropsProcessor } from '../../utils/propsTransformer'
  import type { InputFieldProps, InputFieldEmits } from './types'
  import { inputFieldPropsDefaults } from './types'
  import type { FormContext } from '../Form/types'

  const props = withDefaults(
    defineProps<InputFieldProps>(),
    inputFieldPropsDefaults
  )
  const emit = defineEmits<InputFieldEmits>()

  // 表单上下文注入
  const formContext = inject<FormContext>('spForm', {})

  // 获取属性处理器（单例）
  const propsProcessor = getInputFieldPropsProcessor()

  // 组件引用
  const fieldContainerRef = ref<InstanceType<typeof FieldContainer>>()
  const inputRef = ref()

  // 简化的双向绑定
  const currentValue = computed({
    get: () => props.value,
    set: (value: string | number | undefined) => {
      emit('update:value', value)
      emit('change', value)
    },
  })

  // 兼容性方法
  const updateValue = (value: string | number | undefined) => {
    currentValue.value = value
  }

  // 从表单上下文继承配置
  const computedSize = computed(
    () => props.size || formContext?.size || 'medium'
  )
  const computedDisabled = computed(
    () => props.disabled || formContext?.disabled || false
  )
  const computedShowMessage = computed(
    () => props.showMessage ?? formContext?.showMessage ?? true
  )

  // 使用 Input 逻辑 Composable
  const inputLogic = useInputLogic(props, emit, inputRef)
  const {
    actualType,
    dynamicSizeVars,
    hasValue,
    telPrefix,
    clear: clearInput,
  } = inputLogic

  // 映射自定义类型到 BaseInput 支持的类型
  const mappedInputType = computed(() => {
    const type = actualType.value
    return type === 'smscode' ? 'text' : (type as any)
  })

  // 计算实际的 inputmode
  const actualInputMode = computed(() => {
    if (props.type === 'smscode') return 'numeric'
    if (props.inputmode === 'smscode') return undefined
    return props.inputmode
  })

  // 计算状态（用于控件系统）
  const computedStates = {
    hasValue,
    computedDisabled,
    computedShowMessage,
  }

  // 使用控件系统
  const {
    createControlContext,
    hasPrependControls,
    hasAppendControls,
    hasPrependOuterControls,
    hasAppendOuterControls,
    handleControlEvent,
  } = useInputControls(props, emit, inputLogic, computedStates)

  // BaseInput 属性集合
  const baseInputProps = (slotProps: any) =>
    propsProcessor.createBaseInputProps(props, slotProps, {
      mappedInputType,
      actualInputMode,
    })

  // 传递给 FieldContainer 的属性
  const containerProps = computed(() =>
    propsProcessor.createContainerProps(props, {
      currentValue,
      computedDisabled,
      computedSize,
      computedShowMessage,
      isSearchLoading: inputLogic.isSearchLoading,
      showTelControls: inputLogic.showTelControls,
      telPrefix,
    })
  )

  // 统一的事件处理函数
  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
    if (event.key === 'Enter') {
      emit('enter', event)
    }
  }

  const handleClear = () => {
    clearInput()
    updateValue(undefined)
    emit('clear')
  }

  // BaseInput 事件处理器集合
  const inputEventHandlers = (slotProps: any) => ({
    input: slotProps.onInput,
    change: slotProps.onChange,
    focus: slotProps.onFocus,
    blur: slotProps.onBlur,
    keydown: slotProps.onKeydown,
    keyup: (event: KeyboardEvent) => emit('keyup', event),
    keypress: (event: KeyboardEvent) => emit('keypress', event),
    click: (event: MouseEvent) => emit('click', event),
  })

  // FieldContainer 事件处理器集合
  const containerEventHandlers = computed(() => ({
    'update:value': updateValue,
    input: (event: Event) => emit('input', event),
    change: () => emit('change', currentValue.value),
    focus: (event: FocusEvent) => emit('focus', event),
    blur: (event: FocusEvent) => emit('blur', event),
    keydown: handleKeydown,
    validate: (name: string, isValid: boolean, message: string) =>
      emit('validate', name, isValid, message),
    'click:prepend': (event: MouseEvent) => emit('click:prepend', event),
    'click:prepend-inner': (event: MouseEvent) =>
      emit('click:prepend-inner', event),
    'click:append': (event: MouseEvent) => emit('click:append', event),
    'click:append-inner': (event: MouseEvent) =>
      emit('click:append-inner', event),
  }))

  // 外部点击失焦处理
  const handleOutsideClick = (event: MouseEvent) => {
    if (!fieldContainerRef.value) return
    const containerElement = fieldContainerRef.value.wrapper
    if (containerElement && !containerElement.contains(event.target as Node)) {
      blur()
    }
  }

  onMounted(() => {
    document.addEventListener('click', handleOutsideClick)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleOutsideClick)
  })

  // 暴露的方法
  const focus = () => inputRef.value?.focus()
  const blur = () => inputRef.value?.blur()
  const select = () => inputRef.value?.select()
  const clear = () => handleClear()
  const validate = async (): Promise<boolean> =>
    (await fieldContainerRef.value?.validate?.()) || false
  const resetField = () => fieldContainerRef.value?.resetField?.()
  const clearValidate = () => fieldContainerRef.value?.clearValidate?.()

  // 暴露给外部使用
  defineExpose({
    focus,
    blur,
    select,
    clear,
    validate,
    resetField,
    clearValidate,
    get input() {
      return inputRef.value || null
    },
    get wrapper() {
      return fieldContainerRef.value?.wrapper || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpInputField',
  }
</script>
