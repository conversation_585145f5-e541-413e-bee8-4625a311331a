// VField 组件样式
// 简化版的 Vuetify VField 样式

.v-field {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 56px;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  cursor: text;

  // 基础状态
  &--disabled {
    pointer-events: none;
    opacity: 0.6;
  }

  &--error {
    .v-field__outline,
    .v-field__field::after {
      border-color: #f44336;
    }
  }

  &--focused {
    .v-field__outline,
    .v-field__field::after {
      border-color: #2196f3;
      border-width: 2px;
    }
  }

  // 变体样式
  &--variant-filled {
    background-color: #f5f5f5;
    border-radius: 4px 4px 0 0;

    .v-field__field {
      padding: 16px 12px 8px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background-color: rgba(0, 0, 0, 0.42);
        transition: all 0.2s ease-in-out;
      }
    }

    &.v-field--focused .v-field__field::after {
      height: 2px;
      background-color: #2196f3;
    }
  }

  &--variant-outlined {
    background-color: transparent;

    .v-field__field {
      padding: 16px 12px;
    }
  }

  &--variant-underlined {
    background-color: transparent;

    .v-field__field {
      padding: 8px 0 4px;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background-color: rgba(0, 0, 0, 0.42);
        transition: all 0.2s ease-in-out;
      }
    }

    &.v-field--focused .v-field__field::after {
      height: 2px;
      background-color: #2196f3;
    }
  }

  &--variant-solo {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .v-field__field {
      padding: 16px 12px;
    }
  }

  &--variant-plain {
    background-color: transparent;

    .v-field__field {
      padding: 8px 0;
    }
  }

  // 布局相关
  &--prepended {
    .v-field__field {
      padding-left: 4px;
    }
  }

  &--appended {
    .v-field__field {
      padding-right: 4px;
    }
  }

  &--center-affix {
    .v-field__prepend-inner,
    .v-field__append-inner {
      align-items: center;
    }
  }

  &--single-line {
    .v-field__label {
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &--flat {
    box-shadow: none !important;
  }

  &--reverse {
    flex-direction: row-reverse;
  }
}

// 背景覆盖层
.v-field__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;

  .v-field--active & {
    opacity: 0.04;
    background-color: currentColor;
  }
}

// 加载条
.v-field__loader {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  overflow: hidden;

  &-progress {
    height: 100%;
    background-color: #2196f3;
    animation: v-field-loader 2s infinite;
  }
}

@keyframes v-field-loader {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 字段主体
.v-field__field {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  min-height: inherit;
}

// 标签
.v-field__label {
  position: absolute;
  left: 0;
  top: 16px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 16px;
  line-height: 1;
  pointer-events: none;
  transition: all 0.2s ease-in-out;
  transform-origin: left top;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &--floating {
    top: 8px;
    font-size: 12px;
    transform: scale(0.75);
  }

  &--active {
    color: #2196f3;
  }

  &--focused {
    color: #2196f3;
  }

  .v-field--variant-outlined & {
    background-color: #fff;
    padding: 0 4px;
    margin-left: -4px;
  }
}

// 输入元素
.v-field__input {
  flex: 1;
  min-height: 24px;
  outline: none;
  border: none;
  background: transparent;
  color: inherit;
  font-size: 16px;
  line-height: 1.5;

  &::placeholder {
    color: rgba(0, 0, 0, 0.38);
  }
}

// 前置/后置区域
.v-field__prepend-inner,
.v-field__append-inner {
  display: flex;
  align-items: flex-start;
  padding: 8px;
  min-height: inherit;
}

.v-field__prepend-inner {
  margin-right: 4px;
}

.v-field__append-inner {
  margin-left: 4px;
}

// 图标
.v-field__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.54);
  transition: color 0.2s ease-in-out;

  &:hover {
    color: rgba(0, 0, 0, 0.87);
  }
}

// 清除按钮
.v-field__clearable {
  display: flex;
  align-items: center;
  margin-left: 4px;
}

// 轮廓线（outlined 变体）
.v-field__outline {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  pointer-events: none;

  .v-field--variant-outlined & {
    border: 1px solid rgba(0, 0, 0, 0.38);
    transition: border-color 0.2s ease-in-out;

    &--focused {
      border-color: #2196f3;
      border-width: 2px;
    }
  }

  &__start,
  &__end {
    border: inherit;
    border-radius: inherit;
  }

  &__start {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    width: 12px;
  }

  &__end {
    border-left: none;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    flex: 1;
  }

  &__notch {
    display: flex;
    align-items: center;
    border-top: inherit;
    border-bottom: inherit;
    max-width: calc(100% - 24px);
  }
}
