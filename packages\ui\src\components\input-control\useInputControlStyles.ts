/**
 * InputControl 样式系统
 * 复用 FieldContainer 的样式逻辑和类名，保持样式兼容性
 */

import { computed } from 'vue'
import { bemHelper } from '@speed-ui/config'
import type { InputControlProps } from './types'

const bem = bemHelper('field-container')

export function useInputControlStyles(props: InputControlProps) {
  // ===== 包装器样式 =====
  const wrapperClasses = computed(() => {
    const classes = [bem('wrapper')]

    // 变体样式
    classes.push(bem('wrapper', props.variant))

    // 状态样式
    if (props.disabled) classes.push(bem('wrapper', 'disabled'))
    if (props.readonly) classes.push(bem('wrapper', 'readonly'))
    if (props.focused) classes.push(bem('wrapper', 'focused'))
    if (props.loading) classes.push(bem('wrapper', 'loading'))

    // 验证状态样式
    if (props.validateState) {
      classes.push(bem('wrapper', props.validateState))
    }

    // 效果样式
    if (props.effect && props.effect !== 'none') {
      classes.push(bem('wrapper', props.effect))
    }

    // 尺寸样式
    classes.push(bem('wrapper', props.size))

    return classes
  })

  // ===== 输入元素样式 =====
  const inputClasses = computed(() => {
    const classes = [bem('input')]

    // 变体样式
    classes.push(bem('input', props.variant))

    // 状态样式
    if (props.disabled) classes.push(bem('input', 'disabled'))
    if (props.readonly) classes.push(bem('input', 'readonly'))
    if (props.focused) classes.push(bem('input', 'focused'))

    // 验证状态样式
    if (props.validateState) {
      classes.push(bem('input', props.validateState))
    }

    // 尺寸样式
    classes.push(bem('input', props.size))

    return classes
  })

  // ===== 输入元素内联样式 =====
  const inputStyle = computed(() => {
    const style: Record<string, any> = {}

    // 根据尺寸设置字体大小
    const fontSizeMap = {
      small: '14px',
      medium: '16px',
      large: '18px',
    }
    style.fontSize = fontSizeMap[props.size || 'medium']

    return style
  })

  // ===== 前置区域样式 =====
  const prependClasses = computed(() => {
    const classes = [bem('prepend')]

    // 状态样式
    if (props.disabled) classes.push(bem('prepend', 'disabled'))
    if (props.focused) classes.push(bem('prepend', 'focused'))

    // 验证状态样式
    if (props.validateState) {
      classes.push(bem('prepend', props.validateState))
    }

    // 尺寸样式
    classes.push(bem('prepend', props.size))

    return classes
  })

  // ===== 后置区域样式 =====
  const appendClasses = computed(() => {
    const classes = [bem('append')]

    // 状态样式
    if (props.disabled) classes.push(bem('append', 'disabled'))
    if (props.focused) classes.push(bem('append', 'focused'))

    // 验证状态样式
    if (props.validateState) {
      classes.push(bem('append', props.validateState))
    }

    // 尺寸样式
    classes.push(bem('append', props.size))

    return classes
  })

  // ===== 功能区域样式 =====
  const functionsClasses = computed(() => {
    const classes = [bem('functions')]

    // 状态样式
    if (props.disabled) classes.push(bem('functions', 'disabled'))
    if (props.focused) classes.push(bem('functions', 'focused'))

    // 验证状态样式
    if (props.validateState) {
      classes.push(bem('functions', props.validateState))
    }

    // 尺寸样式
    classes.push(bem('functions', props.size))

    return classes
  })

  // ===== 加载动画条样式 =====
  const loadingBarClasses = computed(() => {
    const classes = [bem('loading-bar')]

    // 变体样式
    classes.push(bem('loading-bar', props.variant))

    // 验证状态样式
    if (props.validateState) {
      classes.push(bem('loading-bar', props.validateState))
    }

    return classes
  })

  // ===== 加载进度条样式 =====
  const loadingProgressClasses = computed(() => {
    const classes = [bem('loading-progress')]

    // 变体样式
    classes.push(bem('loading-progress', props.variant))

    // 验证状态样式
    if (props.validateState) {
      classes.push(bem('loading-progress', props.validateState))
    }

    return classes
  })

  return {
    wrapperClasses,
    inputClasses,
    inputStyle,
    prependClasses,
    appendClasses,
    functionsClasses,
    loadingBarClasses,
    loadingProgressClasses,
  }
}
